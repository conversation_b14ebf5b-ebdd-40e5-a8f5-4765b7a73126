import { defineStore } from 'pinia'
import { ref, computed, watch } from 'vue'

export const useChatStore = defineStore('chat', () => {
  // 状态
  const messages = ref([])
  const isLoading = ref(false)
  const currentConversationId = ref(null)
  const conversations = ref([])
  
  // 计算属性
  const currentMessages = computed(() => {
    if (!currentConversationId.value) return messages.value
    const conversation = conversations.value.find(c => c.id === currentConversationId.value)
    return conversation ? conversation.messages : []
  })
  
  const conversationCount = computed(() => conversations.value.length)
  
  // 方法
  const addMessage = (message) => {
    const newMessage = {
      id: Date.now(),
      content: message.content,
      type: message.type, // 'user' 或 'ai'
      timestamp: new Date(),
      ...message
    }
    
    if (currentConversationId.value) {
      const conversation = conversations.value.find(c => c.id === currentConversationId.value)
      if (conversation) {
        conversation.messages.push(newMessage)
        conversation.updatedAt = new Date()
      }
    } else {
      messages.value.push(newMessage)
    }
  }
  
  const createNewConversation = () => {
    const newConversation = {
      id: Date.now(),
      title: `对话 ${conversations.value.length + 1}`,
      messages: [],
      createdAt: new Date(),
      updatedAt: new Date()
    }
    
    conversations.value.unshift(newConversation)
    currentConversationId.value = newConversation.id
    
    return newConversation.id
  }
  
  const switchConversation = (conversationId) => {
    currentConversationId.value = conversationId
  }
  
  const deleteConversation = (conversationId) => {
    const index = conversations.value.findIndex(c => c.id === conversationId)
    if (index !== -1) {
      conversations.value.splice(index, 1)
      if (currentConversationId.value === conversationId) {
        currentConversationId.value = conversations.value.length > 0 ? conversations.value[0].id : null
      }
    }
  }
  
  const clearCurrentConversation = () => {
    if (currentConversationId.value) {
      const conversation = conversations.value.find(c => c.id === currentConversationId.value)
      if (conversation) {
        conversation.messages = []
      }
    } else {
      messages.value = []
    }
  }
  
  const setLoading = (loading) => {
    isLoading.value = loading
  }

  // 数据持久化
  const saveToStorage = () => {
    try {
      const data = {
        conversations: conversations.value,
        currentConversationId: currentConversationId.value,
        lastSaved: new Date().toISOString()
      }
      localStorage.setItem('chatData', JSON.stringify(data))
    } catch (error) {
      console.error('保存聊天数据失败:', error)
    }
  }

  const loadFromStorage = () => {
    try {
      const saved = localStorage.getItem('chatData')
      if (saved) {
        const data = JSON.parse(saved)
        conversations.value = data.conversations || []
        currentConversationId.value = data.currentConversationId || null
      }
    } catch (error) {
      console.error('加载聊天数据失败:', error)
    }
  }

  // 监听数据变化并自动保存
  const startAutoSave = () => {
    // 监听对话列表变化
    const stopWatchingConversations = watch(
      conversations,
      () => {
        saveToStorage()
      },
      { deep: true }
    )

    // 监听当前对话变化
    const stopWatchingCurrentId = watch(
      currentConversationId,
      () => {
        saveToStorage()
      }
    )

    return () => {
      stopWatchingConversations()
      stopWatchingCurrentId()
    }
  }

  return {
    // 状态
    messages,
    isLoading,
    currentConversationId,
    conversations,
    
    // 计算属性
    currentMessages,
    conversationCount,
    
    // 方法
    addMessage,
    createNewConversation,
    switchConversation,
    deleteConversation,
    clearCurrentConversation,
    setLoading,
    saveToStorage,
    loadFromStorage,
    startAutoSave
  }
})
