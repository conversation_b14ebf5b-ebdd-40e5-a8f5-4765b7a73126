<template>
  <div id="app">
    <NavBar />
    <main class="main-content">
      <ErrorBoundary>
        <router-view />
      </ErrorBoundary>
    </main>
  </div>
</template>

<script>
import { onMounted } from 'vue'
import NavBar from './components/NavBar.vue'
import ErrorBoundary from './components/ErrorBoundary.vue'
import { useSettingsStore } from './stores/settings'

export default {
  name: 'App',
  components: {
    NavBar,
    ErrorBoundary
  },
  setup() {
    const settingsStore = useSettingsStore()

    onMounted(() => {
      // 加载用户设置
      settingsStore.loadSettings()
    })

    return {}
  }
}
</script>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  --primary-color: #4f46e5;
  --primary-hover: #4338ca;
  --secondary-color: #6b7280;
  --background-color: #f9fafb;
  --surface-color: #ffffff;
  --text-primary: #111827;
  --text-secondary: #6b7280;
  --border-color: #e5e7eb;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

[data-theme="dark"] {
  --background-color: #111827;
  --surface-color: #1f2937;
  --text-primary: #f9fafb;
  --text-secondary: #9ca3af;
  --border-color: #374151;
}

#app {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--background-color);
  color: var(--text-primary);
  min-height: 100vh;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.main-content {
  padding-top: 64px; /* 为导航栏留出空间 */
  min-height: calc(100vh - 64px);
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: var(--background-color);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--secondary-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    padding-top: 56px;
  }
}
</style>
