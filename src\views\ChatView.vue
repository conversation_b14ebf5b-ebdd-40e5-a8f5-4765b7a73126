<template>
  <div class="chat-view">
    <div class="chat-container">
      <!-- 聊天消息区域 -->
      <div class="messages-container" ref="messagesContainer">
        <div v-if="currentMessages.length === 0" class="empty-state">
          <div class="empty-icon">💬</div>
          <h3>开始你的英语对话练习</h3>
          <p>输入消息或点击麦克风开始语音对话</p>
        </div>
        
        <div 
          v-for="message in currentMessages" 
          :key="message.id"
          class="message"
          :class="{ 'user-message': message.type === 'user', 'ai-message': message.type === 'ai' }"
        >
          <div class="message-avatar">
            {{ message.type === 'user' ? '👤' : '🤖' }}
          </div>
          <div class="message-content">
            <div class="message-text">{{ message.content }}</div>
            <div class="message-time">
              {{ formatTime(message.timestamp) }}
            </div>
            <div v-if="message.type === 'ai'" class="message-actions">
              <button 
                @click="speakMessage(message.content)"
                class="action-btn"
                :disabled="isSpeaking"
                title="朗读消息"
              >
                🔊
              </button>
            </div>
          </div>
        </div>
        
        <!-- 加载状态 -->
        <div v-if="isLoading" class="message ai-message">
          <div class="message-avatar">🤖</div>
          <div class="message-content">
            <div class="typing-indicator">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 输入区域 -->
      <div class="input-container">
        <div class="input-wrapper">
          <textarea
            v-model="inputMessage"
            @keydown="handleKeydown"
            placeholder="输入你的消息..."
            class="message-input"
            rows="1"
            ref="messageInput"
          ></textarea>
          
          <div class="input-actions">
            <button 
              @click="toggleVoiceInput"
              class="voice-btn"
              :class="{ active: isListening }"
              :disabled="!speechRecognitionSupported"
              :title="isListening ? '停止录音' : '开始语音输入'"
            >
              {{ isListening ? '🔴' : '🎤' }}
            </button>
            
            <button 
              @click="sendMessage"
              class="send-btn"
              :disabled="!inputMessage.trim() || isLoading"
              title="发送消息"
            >
              📤
            </button>
          </div>
        </div>
        
        <!-- 语音识别状态 -->
        <div v-if="isListening" class="voice-status">
          <div class="voice-indicator">
            <div class="pulse"></div>
          </div>
          <span>正在听取语音...</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, nextTick, onMounted, onUnmounted } from 'vue'
import { useChatStore } from '../stores/chat'
import { useSettingsStore } from '../stores/settings'
import { useSpeechRecognition } from '../composables/useSpeechRecognition'
import { useSpeechSynthesis } from '../composables/useSpeechSynthesis'

export default {
  name: 'ChatView',
  setup() {
    const chatStore = useChatStore()
    const settingsStore = useSettingsStore()
    
    const inputMessage = ref('')
    const messagesContainer = ref(null)
    const messageInput = ref(null)
    
    // 计算属性
    const currentMessages = computed(() => chatStore.currentMessages)
    const isLoading = computed(() => chatStore.isLoading)
    
    // 语音功能
    const { 
      isListening, 
      isSupported: speechRecognitionSupported, 
      start: startListening, 
      stop: stopListening 
    } = useSpeechRecognition({
      onResult: (transcript) => {
        inputMessage.value = transcript
      },
      onEnd: () => {
        // 语音识别结束后自动发送消息
        if (inputMessage.value.trim()) {
          sendMessage()
        }
      }
    })
    
    const { speak, isSpeaking } = useSpeechSynthesis()
    
    // 方法
    const sendMessage = async () => {
      const message = inputMessage.value.trim()
      if (!message || isLoading.value) return
      
      // 添加用户消息
      chatStore.addMessage({
        content: message,
        type: 'user'
      })
      
      inputMessage.value = ''
      await scrollToBottom()
      
      // 模拟AI回复（这里应该调用实际的API）
      chatStore.setLoading(true)
      
      try {
        // 这里应该调用后端API
        setTimeout(() => {
          const aiResponse = generateMockResponse()
          chatStore.addMessage({
            content: aiResponse,
            type: 'ai'
          })
          chatStore.setLoading(false)
          
          // 自动朗读AI回复
          if (settingsStore.voiceSettings.autoPlay) {
            speakMessage(aiResponse)
          }
          
          scrollToBottom()
        }, 1000 + Math.random() * 2000)
      } catch (error) {
        console.error('发送消息失败:', error)
        chatStore.setLoading(false)
      }
    }
    
    const toggleVoiceInput = () => {
      if (isListening.value) {
        stopListening()
      } else {
        startListening()
      }
    }
    
    const speakMessage = (text) => {
      speak(text, {
        rate: settingsStore.voiceSettings.rate,
        pitch: settingsStore.voiceSettings.pitch,
        volume: settingsStore.voiceSettings.volume
      })
    }
    
    const handleKeydown = (event) => {
      if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault()
        sendMessage()
      }
    }
    
    const scrollToBottom = async () => {
      await nextTick()
      if (messagesContainer.value) {
        messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
      }
    }
    
    const formatTime = (timestamp) => {
      return new Date(timestamp).toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      })
    }
    
    // 模拟AI回复（实际应用中应该调用API）
    const generateMockResponse = () => {
      const responses = [
        "That's interesting! Can you tell me more about that?",
        "I understand. How do you feel about this situation?",
        "Great question! Let me think about that for a moment.",
        "That sounds challenging. What would you like to do about it?",
        "I see your point. Have you considered other options?",
        "That's a good observation. What made you think of that?",
        "Interesting perspective! Can you give me an example?",
        "I appreciate you sharing that with me. What's your next step?"
      ]
      return responses[Math.floor(Math.random() * responses.length)]
    }
    
    // 自动调整输入框高度
    const adjustTextareaHeight = () => {
      if (messageInput.value) {
        messageInput.value.style.height = 'auto'
        messageInput.value.style.height = messageInput.value.scrollHeight + 'px'
      }
    }
    
    onMounted(() => {
      // 如果没有当前对话，创建一个新的
      if (!chatStore.currentConversationId) {
        chatStore.createNewConversation()
      }
      
      // 监听输入框变化
      if (messageInput.value) {
        messageInput.value.addEventListener('input', adjustTextareaHeight)
      }
    })
    
    onUnmounted(() => {
      if (messageInput.value) {
        messageInput.value.removeEventListener('input', adjustTextareaHeight)
      }
    })
    
    return {
      inputMessage,
      messagesContainer,
      messageInput,
      currentMessages,
      isLoading,
      isListening,
      speechRecognitionSupported,
      isSpeaking,
      sendMessage,
      toggleVoiceInput,
      speakMessage,
      handleKeydown,
      formatTime
    }
  }
}
</script>

<style scoped>
.chat-view {
  height: calc(100vh - 64px);
  display: flex;
  flex-direction: column;
}

.chat-container {
  max-width: 800px;
  margin: 0 auto;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0 1rem;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 1rem 0;
  scroll-behavior: smooth;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  color: var(--text-secondary);
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.empty-state h3 {
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.message {
  display: flex;
  margin-bottom: 1rem;
  animation: fadeIn 0.3s ease;
}

.user-message {
  justify-content: flex-end;
}

.user-message .message-content {
  background-color: var(--primary-color);
  color: white;
  margin-left: 2rem;
}

.ai-message .message-content {
  background-color: var(--surface-color);
  border: 1px solid var(--border-color);
  margin-right: 2rem;
}

.message-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  flex-shrink: 0;
  background-color: var(--background-color);
}

.user-message .message-avatar {
  order: 2;
  margin-left: 0.75rem;
}

.ai-message .message-avatar {
  margin-right: 0.75rem;
}

.message-content {
  max-width: 70%;
  padding: 0.75rem 1rem;
  border-radius: 1rem;
  position: relative;
}

.message-text {
  line-height: 1.5;
  word-wrap: break-word;
}

.message-time {
  font-size: 0.75rem;
  opacity: 0.7;
  margin-top: 0.25rem;
}

.message-actions {
  margin-top: 0.5rem;
}

.action-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.action-btn:hover {
  opacity: 1;
}

.typing-indicator {
  display: flex;
  gap: 0.25rem;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--text-secondary);
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

.input-container {
  padding: 1rem 0;
  border-top: 1px solid var(--border-color);
  background-color: var(--surface-color);
}

.input-wrapper {
  display: flex;
  gap: 0.5rem;
  align-items: flex-end;
}

.message-input {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 1.5rem;
  background-color: var(--background-color);
  color: var(--text-primary);
  resize: none;
  min-height: 44px;
  max-height: 120px;
  font-family: inherit;
  font-size: 1rem;
  line-height: 1.5;
  outline: none;
  transition: border-color 0.2s ease;
}

.message-input:focus {
  border-color: var(--primary-color);
}

.input-actions {
  display: flex;
  gap: 0.5rem;
}

.voice-btn,
.send-btn {
  width: 44px;
  height: 44px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  transition: all 0.2s ease;
}

.voice-btn {
  background-color: var(--background-color);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
}

.voice-btn:hover {
  background-color: var(--border-color);
}

.voice-btn.active {
  background-color: var(--error-color);
  color: white;
  border-color: var(--error-color);
}

.send-btn {
  background-color: var(--primary-color);
  color: white;
}

.send-btn:hover:not(:disabled) {
  background-color: var(--primary-hover);
}

.send-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.voice-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.voice-indicator {
  position: relative;
  width: 12px;
  height: 12px;
}

.pulse {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: var(--error-color);
  animation: pulse 1.5s infinite;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.5);
    opacity: 0.5;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .chat-view {
    height: calc(100vh - 56px);
  }
  
  .chat-container {
    padding: 0 0.5rem;
  }
  
  .message-content {
    max-width: 85%;
  }
  
  .empty-icon {
    font-size: 3rem;
  }
}
</style>
