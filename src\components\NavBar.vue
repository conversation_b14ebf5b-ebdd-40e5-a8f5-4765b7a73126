<template>
  <nav class="navbar">
    <div class="nav-container">
      <!-- Logo和标题 -->
      <div class="nav-brand">
        <router-link to="/" class="brand-link">
          <div class="brand-icon">🗣️</div>
          <span class="brand-text">英语对话练习</span>
        </router-link>
      </div>
      
      <!-- 桌面端导航菜单 -->
      <div class="nav-menu desktop-menu">
        <router-link 
          v-for="item in menuItems" 
          :key="item.name"
          :to="item.path" 
          class="nav-link"
          :class="{ active: $route.name === item.name }"
        >
          <span class="nav-icon">{{ item.icon }}</span>
          <span class="nav-text">{{ item.label }}</span>
        </router-link>
      </div>
      
      <!-- 主题切换按钮 -->
      <div class="nav-actions">
        <button 
          @click="toggleTheme" 
          class="theme-toggle"
          :title="theme === 'light' ? '切换到深色模式' : '切换到浅色模式'"
        >
          {{ theme === 'light' ? '🌙' : '☀️' }}
        </button>
        
        <!-- 移动端菜单按钮 -->
        <button 
          @click="toggleMobileMenu" 
          class="mobile-menu-btn"
          :class="{ active: showMobileMenu }"
        >
          <span></span>
          <span></span>
          <span></span>
        </button>
      </div>
    </div>
    
    <!-- 移动端菜单 -->
    <div class="mobile-menu" :class="{ show: showMobileMenu }">
      <router-link 
        v-for="item in menuItems" 
        :key="item.name"
        :to="item.path" 
        class="mobile-nav-link"
        :class="{ active: $route.name === item.name }"
        @click="closeMobileMenu"
      >
        <span class="nav-icon">{{ item.icon }}</span>
        <span class="nav-text">{{ item.label }}</span>
      </router-link>
    </div>
  </nav>
</template>

<script>
import { ref, computed } from 'vue'
import { useSettingsStore } from '../stores/settings'

export default {
  name: 'NavBar',
  setup() {
    const settingsStore = useSettingsStore()
    const showMobileMenu = ref(false)
    
    const theme = computed(() => settingsStore.theme)
    
    const menuItems = [
      { name: 'Chat', path: '/', label: '对话', icon: '💬' },
      { name: 'History', path: '/history', label: '历史', icon: '📚' },
      { name: 'Settings', path: '/settings', label: '设置', icon: '⚙️' }
    ]
    
    const toggleTheme = () => {
      const newTheme = theme.value === 'light' ? 'dark' : 'light'
      settingsStore.updateTheme(newTheme)
    }
    
    const toggleMobileMenu = () => {
      showMobileMenu.value = !showMobileMenu.value
    }
    
    const closeMobileMenu = () => {
      showMobileMenu.value = false
    }
    
    return {
      theme,
      menuItems,
      showMobileMenu,
      toggleTheme,
      toggleMobileMenu,
      closeMobileMenu
    }
  }
}
</script>

<style scoped>
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: var(--surface-color);
  border-bottom: 1px solid var(--border-color);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
}

.nav-brand {
  flex-shrink: 0;
}

.brand-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: var(--text-primary);
  font-weight: 600;
  font-size: 1.125rem;
}

.brand-icon {
  font-size: 1.5rem;
  margin-right: 0.5rem;
}

.brand-text {
  display: none;
}

.desktop-menu {
  display: none;
  gap: 2rem;
}

.nav-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: var(--text-secondary);
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
}

.nav-link:hover {
  color: var(--primary-color);
  background-color: var(--background-color);
}

.nav-link.active {
  color: var(--primary-color);
  background-color: var(--background-color);
}

.nav-icon {
  margin-right: 0.5rem;
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.theme-toggle {
  background: none;
  border: none;
  font-size: 1.25rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.5rem;
  transition: background-color 0.2s ease;
}

.theme-toggle:hover {
  background-color: var(--background-color);
}

.mobile-menu-btn {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  width: 24px;
  height: 24px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
}

.mobile-menu-btn span {
  width: 100%;
  height: 2px;
  background-color: var(--text-primary);
  transition: all 0.3s ease;
}

.mobile-menu-btn.active span:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.mobile-menu-btn.active span:nth-child(2) {
  opacity: 0;
}

.mobile-menu-btn.active span:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

.mobile-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: var(--surface-color);
  border-bottom: 1px solid var(--border-color);
  transform: translateY(-100%);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.mobile-menu.show {
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
}

.mobile-nav-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: var(--text-secondary);
  font-weight: 500;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  transition: all 0.2s ease;
}

.mobile-nav-link:hover,
.mobile-nav-link.active {
  color: var(--primary-color);
  background-color: var(--background-color);
}

.mobile-nav-link:last-child {
  border-bottom: none;
}

/* 桌面端样式 */
@media (min-width: 768px) {
  .nav-container {
    height: 64px;
  }
  
  .brand-text {
    display: inline;
  }
  
  .desktop-menu {
    display: flex;
  }
  
  .mobile-menu-btn {
    display: none;
  }
  
  .mobile-menu {
    display: none;
  }
}

/* 移动端样式 */
@media (max-width: 767px) {
  .nav-container {
    height: 56px;
  }
  
  .brand-link {
    font-size: 1rem;
  }
  
  .brand-icon {
    font-size: 1.25rem;
  }
}
</style>
